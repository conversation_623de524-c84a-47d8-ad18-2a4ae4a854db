import { useState, useEffect } from 'react';
import logo from '../assets/tb.png';

const Header = () => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [activeSection, setActiveSection] = useState('home');

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const navItems = [
    { href: '#home', label: 'Home' },
    { href: '#about', label: 'About' },
  ];

  return (
    <header className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
      isScrolled 
        ? 'bg-dark-bg/98 shadow-[0_2px_20px_rgba(16,18,27,0.3)]' 
        : 'bg-dark-bg/95'
    } backdrop-blur-md border-b border-gold/30`}>
      <div className="max-w-6xl mx-auto px-8 flex justify-between items-center h-[70px] md:h-[60px]">
        {/* Logo and Brand */}
        <div className="flex items-center gap-4">
          <img 
            src={logo} 
            alt="Titus Bortier" 
            className={`h-11 md:h-9 w-auto transition-all duration-300 ${
              activeSection === 'home' 
                ? 'drop-shadow-[0_0_15px_rgba(228,185,81,0.8)]' 
                : 'hover:drop-shadow-[0_0_15px_rgba(228,185,81,0.8)]'
            } hover:scale-105`}
          />
          <span className="text-xl md:text-lg font-bold text-text-primary font-cinzel tracking-tight">
            Titus Bortier
          </span>
        </div>
        
        {/* Desktop Navigation */}
        <nav className="hidden md:flex gap-8 items-center">
          {navItems.map((item) => (
            <a
              key={item.href}
              href={item.href}
              className={`relative px-4 py-2 font-medium text-base rounded-md transition-all duration-300 ${
                activeSection === item.href.slice(1)
                  ? 'text-gold bg-gold/10'
                  : 'text-text-muted hover:text-gold hover:bg-gold/10'
              }`}
            >
              {item.label}
              <div className={`absolute bottom-[-2px] left-1/2 transform -translate-x-1/2 h-0.5 bg-gold transition-all duration-300 ${
                activeSection === item.href.slice(1) ? 'w-4/5' : 'w-0 group-hover:w-4/5'
              }`}></div>
            </a>
          ))}
        </nav>

        {/* Mobile Menu Button */}
        <button
          className="md:hidden p-2 z-50"
          onClick={toggleMobileMenu}
        >
          <div className="w-6 h-5 relative">
            <span className={`block absolute h-0.5 w-full bg-text-secondary rounded transition-all duration-300 ${
              isMobileMenuOpen ? 'top-2 rotate-45 bg-gold' : 'top-0'
            }`}></span>
            <span className={`block absolute h-0.5 w-full bg-text-secondary rounded transition-all duration-300 ${
              isMobileMenuOpen ? 'opacity-0 bg-gold' : 'top-2'
            }`}></span>
            <span className={`block absolute h-0.5 w-full bg-text-secondary rounded transition-all duration-300 ${
              isMobileMenuOpen ? 'top-2 -rotate-45 bg-gold' : 'top-4'
            }`}></span>
          </div>
        </button>

        {/* Mobile Navigation */}
        <nav className={`md:hidden absolute top-full left-0 right-0 bg-dark-bg/98 backdrop-blur-md border-b border-gold/30 shadow-[0_4px_20px_rgba(16,18,27,0.3)] transition-all duration-300 ${
          isMobileMenuOpen 
            ? 'translate-y-0 opacity-100 visible' 
            : '-translate-y-full opacity-0 invisible'
        }`}>
          {navItems.map((item, index) => (
            <a
              key={item.href}
              href={item.href}
              className="block px-8 py-4 text-text-muted font-medium text-lg border-b border-gold/20 last:border-b-0 transition-all duration-300 hover:text-gold hover:bg-gold/10 hover:pl-10"
              onClick={toggleMobileMenu}
            >
              {item.label}
            </a>
          ))}
        </nav>
      </div>
    </header>
  );
};

export default Header;

