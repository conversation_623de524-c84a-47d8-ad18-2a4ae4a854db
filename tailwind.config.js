/** @type {import('tailwindcss').Config} */
module.exports = {
  content: ['./src/**/*.{js,ts,jsx,tsx,mdx}'],
  theme: {
    extend: {
      colors: {
        'dark-bg': '#10121b',
        'gold': '#e4b951',
        'gold-hover': '#d4a441',
        'text-primary': '#ffffff',
        'text-secondary': '#e0e0e0',
        'text-muted': '#b0b0b0',
        'text-subtle': '#888888',
      },
      fontFamily: {
        'cinzel': ['Cinzel', 'serif'],
      },
      boxShadow: {
        'gold-glow': '0 0 15px rgba(228, 185, 81, 0.8)',
        'gold-glow-lg': '0 10px 25px rgba(228, 185, 81, 0.4)',
        'gold-glow-xl': '0 0 30px rgba(228, 185, 81, 1)',
      },
      dropShadow: {
        'gold-glow': '0 0 20px rgba(228, 185, 81, 1)',
        'gold-glow-lg': '0 0 30px rgba(228, 185, 81, 1)',
      },
    },
  },
  plugins: [],
}