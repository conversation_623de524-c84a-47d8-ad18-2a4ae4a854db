import Header from './components/Header'
import profilePic from './assets/tprof2.png'

function App() {
  return (
    <div className="bg-dark-bg min-h-screen">
      <Header />
      <main className="pt-[70px] md:pt-[60px]">
        {/* Hero Section */}
        <section id="home" className="min-h-screen flex items-center justify-center bg-dark-bg text-center relative overflow-hidden">
          <div className="relative z-10 max-w-4xl px-8">
            {/* Profile Image */}
            <div className="mb-8">
              <img 
                src={profilePic} 
                alt="Pastor <PERSON>" 
                className="w-48 h-48 md:w-56 md:h-56 rounded-full object-cover border-4 border-gold shadow-[0_8px_32px_rgba(228,185,81,0.3)] transition-all duration-300 hover:shadow-[0_8px_32px_rgba(228,185,81,0.6)] hover:scale-105 mx-auto"
              />
            </div>
            
            <h1 className="text-4xl md:text-6xl font-bold mb-6 text-text-primary font-cinzel">
              Pastor <PERSON>
            </h1>
            
            <p className="text-xl md:text-2xl mb-4 text-text-muted font-light">
              Pastor-Teacher • Conference Speaker • Writer • Businessman
            </p>
            
            <p className="text-lg mb-10 text-text-subtle italic">
              Passionate about discipleship, prayer, and spiritual transformation
            </p>
            
            <div className="flex gap-6 justify-center flex-wrap">
              <a 
                href="#ministry" 
                className="inline-block px-8 py-4 bg-gold text-dark-bg font-semibold text-lg rounded-full border-2 border-gold transition-all duration-300 hover:bg-transparent hover:text-gold hover:-translate-y-1 hover:shadow-gold-glow-lg"
              >
                Explore Ministry
              </a>
              <a 
                href="#contact" 
                className="inline-block px-8 py-4 bg-transparent text-text-secondary font-semibold text-lg rounded-full border-2 border-text-secondary transition-all duration-300 hover:bg-gold hover:text-dark-bg hover:border-gold hover:-translate-y-1 hover:shadow-gold-glow-lg"
              >
                Connect With Me
              </a>
            </div>
          </div>
        </section>
        
        {/* About Section */}
        <section id="about" className="py-20 bg-dark-bg">
          <div className="max-w-4xl mx-auto px-8 text-center">
            <h2 className="text-3xl md:text-4xl font-bold mb-8 text-text-primary font-cinzel relative">
              About Pastor Titus
              <div className="absolute bottom-[-10px] left-1/2 transform -translate-x-1/2 w-16 h-1 bg-gradient-to-r from-gold to-gold-hover rounded-full"></div>
            </h2>
            
            <p className="text-lg md:text-xl text-text-muted mb-6 leading-relaxed">
              For over fifteen years, Pastor Titus Bortier has been a dedicated pastor-teacher, conference speaker, writer, and businessman. His passion lies in discipleship, prayer, and spiritual transformation, having mentored and equipped countless believers to live Spirit-filled lives—many of whom have gone on to become pastors themselves.
            </p>
            
            <p className="text-lg md:text-xl text-text-muted leading-relaxed">
              His work beautifully blends ministry and innovation, inspiring individuals, families, and churches to walk in God's purpose with faith, passion, and perseverance.
            </p>
          </div>
        </section>
      </main>
    </div>
  )
}

export default App


