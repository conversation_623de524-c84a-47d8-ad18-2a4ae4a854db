/* Header Styles */
.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(16, 18, 27, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(228, 185, 81, 0.3);
  transition: all 0.3s ease;
}

.header.scrolled {
  background: rgba(16, 18, 27, 0.98);
  box-shadow: 0 2px 20px rgba(16, 18, 27, 0.3);
}

.header-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 70px;
}

/* Logo and Brand */
.logo-container {
  display: flex;
  align-items: center;
  gap: 1rem;
  text-decoration: none;
}

.logo {
  height: 45px;
  width: auto;
  transition: all 0.3s ease;
  filter: drop-shadow(0 0 0px rgba(228, 185, 81, 0));
}

.logo:hover {
  transform: scale(1.05);
  filter: drop-shadow(0 0 15px rgba(228, 185, 81, 0.8));
}

.brand-name {
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
  letter-spacing: -0.5px;
}

/* Desktop Navigation */
.desktop-nav {
  display: flex;
  gap: 2rem;
  align-items: center;
}

.nav-link {
  text-decoration: none;
  color: #b0b0b0;
  font-weight: 500;
  font-size: 1rem;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  transition: all 0.3s ease;
  position: relative;
}

.nav-link:hover {
  color: #e4b951;
  background: rgba(228, 185, 81, 0.1);
}

.nav-link.active {
  color: #e4b951;
  background: rgba(228, 185, 81, 0.1);
}

.nav-link::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 50%;
  width: 0;
  height: 2px;
  background: #e4b951;
  transition: all 0.3s ease;
  transform: translateX(-50%);
}

.nav-link:hover::after,
.nav-link.active::after {
  width: 80%;
}

/* Mobile Menu Button */
.mobile-menu-button {
  display: none;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  z-index: 1001;
}

.hamburger {
  display: flex;
  flex-direction: column;
  width: 25px;
  height: 20px;
  position: relative;
}

.hamburger span {
  display: block;
  height: 3px;
  width: 100%;
  background: #e0e0e0;
  border-radius: 2px;
  transition: all 0.3s ease;
  transform-origin: center;
}

.hamburger span:nth-child(1) {
  margin-bottom: 5px;
}

.hamburger span:nth-child(2) {
  margin-bottom: 5px;
}

.hamburger.open span {
  background: #e4b951;
}

.hamburger.open span:nth-child(1) {
  transform: rotate(45deg) translate(6px, 6px);
}

.hamburger.open span:nth-child(2) {
  opacity: 0;
}

.hamburger.open span:nth-child(3) {
  transform: rotate(-45deg) translate(6px, -6px);
}

/* Mobile Navigation */
.mobile-nav {
  display: none;
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: rgba(16, 18, 27, 0.98);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(228, 185, 81, 0.3);
  box-shadow: 0 4px 20px rgba(16, 18, 27, 0.3);
  transform: translateY(-100%);
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.mobile-nav.open {
  transform: translateY(0);
  opacity: 1;
  visibility: visible;
}

.mobile-nav-link {
  display: block;
  padding: 1rem 2rem;
  text-decoration: none;
  color: #b0b0b0;
  font-weight: 500;
  font-size: 1.1rem;
  border-bottom: 1px solid rgba(228, 185, 81, 0.2);
  transition: all 0.3s ease;
}

.mobile-nav-link:hover {
  color: #e4b951;
  background: rgba(228, 185, 81, 0.1);
  padding-left: 2.5rem;
}

.mobile-nav-link:last-child {
  border-bottom: none;
}

/* Responsive Design */
@media (max-width: 768px) {
  .header-container {
    padding: 0 1rem;
    height: 60px;
  }

  .logo {
    height: 35px;
  }

  .brand-name {
    font-size: 1.2rem;
  }

  .desktop-nav {
    display: none;
  }

  .mobile-menu-button {
    display: block;
  }

  .mobile-nav {
    display: block;
  }
}

@media (max-width: 480px) {
  .header-container {
    padding: 0 0.75rem;
  }

  .brand-name {
    font-size: 1rem;
  }

  .logo {
    height: 30px;
  }
}

/* Smooth scrolling for anchor links */
html {
  scroll-behavior: smooth;
}

/* Add padding to body to account for fixed header */
body {
  padding-top: 70px;
  background-color: #10121b;
}

@media (max-width: 768px) {
  body {
    padding-top: 60px;
  }
}


