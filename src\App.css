/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  line-height: 1.6;
  background-color: #10121b;
  color: #e0e0e0;
}

#root {
  min-height: 100vh;
  background-color: #10121b;
}

/* Main content */
.main-content {
  padding-top: 0;
  background-color: #10121b;
}

/* Hero section */
.hero-section {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #10121b;
  color: #e0e0e0;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.hero-content {
  position: relative;
  z-index: 1;
  max-width: 800px;
  padding: 0 2rem;
}

.profile-image-container {
  margin-bottom: 2rem;
}

.profile-image {
  width: 200px;
  height: 200px;
  border-radius: 50%;
  object-fit: cover;
  border: 4px solid #e4b951;
  box-shadow: 0 8px 32px rgba(228, 185, 81, 0.3);
  transition: all 0.3s ease;
}

.profile-image:hover {
  box-shadow: 0 8px 32px rgba(228, 185, 81, 0.6);
  transform: scale(1.05);
}

.hero-content h1 {
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  line-height: 1.2;
  color: #ffffff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-content p {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: #b0b0b0;
  font-weight: 300;
}

.hero-subtitle {
  font-size: 1.2rem !important;
  margin-bottom: 2.5rem !important;
  color: #888888 !important;
  font-style: italic;
}

.hero-buttons {
  display: flex;
  gap: 1.5rem;
  justify-content: center;
  flex-wrap: wrap;
}

/* Button styles */
.btn {
  display: inline-block;
  padding: 1rem 2rem;
  text-decoration: none;
  border-radius: 50px;
  font-weight: 600;
  font-size: 1.1rem;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  cursor: pointer;
}

.btn-primary {
  background: #e4b951;
  color: #10121b;
  border-color: #e4b951;
  font-weight: 600;
}

.btn-primary:hover {
  background: transparent;
  color: #e4b951;
  border-color: #e4b951;
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(228, 185, 81, 0.4);
}

.btn-secondary {
  background: transparent;
  color: #e0e0e0;
  border-color: #e0e0e0;
}

.btn-secondary:hover {
  background: #e4b951;
  color: #10121b;
  border-color: #e4b951;
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(228, 185, 81, 0.4);
}

/* Section styles */
.section {
  padding: 5rem 0;
  background-color: #10121b;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  text-align: center;
}

.section h2 {
  font-size: 2.5rem;
  margin-bottom: 2rem;
  color: #ffffff;
  font-weight: 700;
  text-align: center;
  position: relative;
}

.section h2::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, #e4b951, #d4a441);
  border-radius: 2px;
}

.section p {
  font-size: 1.2rem;
  color: #b0b0b0;
  max-width: 800px;
  margin: 0 auto 1.5rem auto;
  line-height: 1.8;
}

/* Responsive design */
@media (max-width: 768px) {
  .profile-image {
    width: 150px;
    height: 150px;
  }
  
  .hero-content h1 {
    font-size: 2.5rem;
  }
  
  .hero-content p {
    font-size: 1.2rem;
  }
  
  .hero-buttons {
    flex-direction: column;
    align-items: center;
  }
  
  .btn {
    width: 200px;
  }
  
  .section h2 {
    font-size: 2rem;
  }
  
  .section p {
    font-size: 1.1rem;
  }
}

@media (max-width: 480px) {
  .profile-image {
    width: 120px;
    height: 120px;
  }
  
  .hero-content {
    padding: 0 1rem;
  }
  
  .hero-content h1 {
    font-size: 2rem;
  }
  
  .hero-content p {
    font-size: 1rem;
  }
  
  .container {
    padding: 0 1rem;
  }
}

